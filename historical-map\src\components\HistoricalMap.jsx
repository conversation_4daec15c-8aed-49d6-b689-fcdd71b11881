import { useState, useEffect, useRef } from 'react'
import * as d3 from 'd3'
import './HistoricalMap.css'

const HistoricalMap = () => {
  const [selectedYear, setSelectedYear] = useState(2000)
  const [mapData, setMapData] = useState(null)
  const [loading, setLoading] = useState(false)
  const svgRef = useRef()

  // Available years based on the geojson files
  const availableYears = [
    'bc123000', 'bc10000', 'bc8000', 'bc5000', 'bc4000', 'bc3000', 'bc2000', 'bc1500', 'bc1000', 'bc700', 'bc500', 'bc400', 'bc323', 'bc300', 'bc200', 'bc100', 'bc1',
    100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200, 1279, 1300, 1400, 1492, 1500, 1530, 1600, 1650, 1700, 1715, 1783, 1800, 1815, 1880, 1900, 1914, 1920, 1930, 1938, 1945, 1960, 1994, 2000, 2010
  ]

  // Load map data for selected year
  useEffect(() => {
    const loadMapData = async () => {
      setLoading(true)
      try {
        const filename = `world_${selectedYear}.geojson`
        const response = await fetch(`../historical-basemaps/geojson/${filename}`)
        if (!response.ok) {
          throw new Error(`Failed to load ${filename}`)
        }
        const data = await response.json()
        setMapData(data)
      } catch (error) {
        console.error('Error loading map data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadMapData()
  }, [selectedYear])

  // Render map using D3
  useEffect(() => {
    if (!mapData || !svgRef.current) return

    const svg = d3.select(svgRef.current)
    svg.selectAll("*").remove() // Clear previous render

    const width = 1200
    const height = 600

    // Set up projection
    const projection = d3.geoNaturalEarth1()
      .scale(180)
      .translate([width / 2, height / 2])

    const path = d3.geoPath().projection(projection)

    // Create main group
    const g = svg.append("g")

    // Color scale for different countries/regions
    const colorScale = d3.scaleOrdinal()
      .domain(mapData.features.map((d, i) => i))
      .range([
        "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
        "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
        "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2",
        "#A3E4D7", "#F9E79F", "#D5A6BD", "#AED6F1", "#A9DFBF",
        "#FAD7A0", "#D2B4DE", "#AED6F1", "#A3E4D7", "#F7DC6F"
      ])

    // Add countries
    g.selectAll(".country")
      .data(mapData.features)
      .enter().append("path")
      .attr("class", "country")
      .attr("d", path)
      .attr("fill", (d, i) => colorScale(i))
      .attr("stroke", "rgba(255, 255, 255, 0.8)")
      .attr("stroke-width", 0.8)
      .style("opacity", 0.85)
      .on("mouseover", function(event, d) {
        d3.select(this).style("opacity", 1)
      })
      .on("mouseout", function() {
        d3.select(this).style("opacity", 0.85)
      })

    // Add country labels (initially hidden)
    g.selectAll(".country-label")
      .data(mapData.features.filter(d => d.properties && d.properties.NAME))
      .enter().append("text")
      .attr("class", "country-label")
      .attr("transform", function(d) {
        const centroid = path.centroid(d)
        return `translate(${centroid[0]}, ${centroid[1]})`
      })
      .attr("text-anchor", "middle")
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .style("font-weight", "600")
      .style("fill", "#2d3748")
      .style("stroke", "white")
      .style("stroke-width", "2px")
      .style("paint-order", "stroke")
      .style("pointer-events", "none")
      .style("opacity", 0) // Initially hidden
      .text(function(d) {
        // Truncate long country names
        const name = d.properties.NAME
        return name.length > 15 ? name.substring(0, 15) + "..." : name
      })

    // Add zoom behavior
    const zoom = d3.zoom()
      .scaleExtent([0.5, 8])
      .on("zoom", (event) => {
        g.attr("transform", event.transform)

        // Show labels only when zoomed in (scale > 1.5)
        const scale = event.transform.k
        const showLabels = scale > 1.5

        g.selectAll(".country-label")
          .style("opacity", showLabels ? Math.min(1, (scale - 1.5) * 2) : 0)
          .style("font-size", `${Math.max(10, 12 * Math.min(scale, 3))}px`)
      })

    svg.call(zoom)

  }, [mapData])

  const formatYearDisplay = (year) => {
    if (typeof year === 'string' && year.startsWith('bc')) {
      const bcYear = year.replace('bc', '')
      return `${parseInt(bcYear).toLocaleString()} BC`
    }
    return `${year} AD`
  }

  return (
    <div className="historical-map">
      <div className="controls">
        <div className="year-selector">
          <label htmlFor="year-select">Select Year:</label>
          <select 
            id="year-select"
            value={selectedYear} 
            onChange={(e) => setSelectedYear(e.target.value)}
            disabled={loading}
          >
            {availableYears.map(year => (
              <option key={year} value={year}>
                {formatYearDisplay(year)}
              </option>
            ))}
          </select>
        </div>
        <div className="current-year">
          <h2>{formatYearDisplay(selectedYear)}</h2>
          <p className="zoom-hint">Zoom in to see country names</p>
        </div>
      </div>
      
      <div className="map-container">
        {loading && <div className="loading">Loading map data...</div>}
        <svg
          ref={svgRef}
          width="1200"
          height="600"
          style={{ border: '1px solid #e2e8f0', background: '#f8f9fa', borderRadius: '8px' }}
        />
      </div>
    </div>
  )
}

export default HistoricalMap
