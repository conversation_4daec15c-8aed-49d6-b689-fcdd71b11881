import { useState, useEffect, useRef } from 'react'
import * as d3 from 'd3'
import './HistoricalMap.css'

// Helper function to get country abbreviations
const getCountryAbbreviation = (name) => {
  const abbreviations = {
    'United States': 'USA',
    'United Kingdom': 'UK',
    'Russian Federation': 'Russia',
    'People\'s Republic of China': 'China',
    'Federal Republic of Germany': 'Germany',
    'French Republic': 'France',
    'Kingdom of Spain': 'Spain',
    'Italian Republic': 'Italy',
    'Republic of India': 'India',
    'Federative Republic of Brazil': 'Brazil',
    'Commonwealth of Australia': 'Australia',
    'Dominion of Canada': 'Canada',
    'United Mexican States': 'Mexico',
    'Republic of South Africa': 'S.Africa',
    'Argentine Republic': 'Argentina',
    'Oriental Republic of Uruguay': 'Uruguay',
    'Republic of Chile': 'Chile',
    'Republic of Peru': 'Peru',
    'Republic of Colombia': 'Colombia',
    'Bolivarian Republic of Venezuela': 'Venezuela',
    'Republic of Ecuador': 'Ecuador',
    'Plurinational State of Bolivia': 'Bolivia',
    'Republic of Paraguay': 'Paraguay',
    'Co-operative Republic of Guyana': 'Guyana',
    'Republic of Suriname': 'Suriname'
  }

  return abbreviations[name] || (name.length > 6 ? name.substring(0, 6) + "." : name)
}

// Helper function to get country codes for very small countries
const getCountryCode = (name) => {
  const codes = {
    'United States': 'US',
    'United Kingdom': 'GB',
    'Russian Federation': 'RU',
    'People\'s Republic of China': 'CN',
    'Federal Republic of Germany': 'DE',
    'French Republic': 'FR',
    'Kingdom of Spain': 'ES',
    'Italian Republic': 'IT',
    'Republic of India': 'IN',
    'Federative Republic of Brazil': 'BR',
    'Commonwealth of Australia': 'AU',
    'Dominion of Canada': 'CA',
    'United Mexican States': 'MX',
    'Republic of South Africa': 'ZA',
    'Argentine Republic': 'AR',
    'Oriental Republic of Uruguay': 'UY',
    'Republic of Chile': 'CL',
    'Republic of Peru': 'PE',
    'Republic of Colombia': 'CO',
    'Bolivarian Republic of Venezuela': 'VE',
    'Republic of Ecuador': 'EC',
    'Plurinational State of Bolivia': 'BO',
    'Republic of Paraguay': 'PY',
    'Co-operative Republic of Guyana': 'GY',
    'Republic of Suriname': 'SR',
    'Vatican City': 'VA',
    'San Marino': 'SM',
    'Monaco': 'MC',
    'Liechtenstein': 'LI',
    'Andorra': 'AD',
    'Malta': 'MT',
    'Luxembourg': 'LU'
  }

  return codes[name] || name.substring(0, 2).toUpperCase()
}

const HistoricalMap = () => {
  const [selectedYear, setSelectedYear] = useState(2000)
  const [mapData, setMapData] = useState(null)
  const [loading, setLoading] = useState(false)
  const svgRef = useRef()

  // Available years based on the geojson files
  const availableYears = [
    'bc123000', 'bc10000', 'bc8000', 'bc5000', 'bc4000', 'bc3000', 'bc2000', 'bc1500', 'bc1000', 'bc700', 'bc500', 'bc400', 'bc323', 'bc300', 'bc200', 'bc100', 'bc1',
    100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200, 1279, 1300, 1400, 1492, 1500, 1530, 1600, 1650, 1700, 1715, 1783, 1800, 1815, 1880, 1900, 1914, 1920, 1930, 1938, 1945, 1960, 1994, 2000, 2010
  ]

  // Load map data for selected year
  useEffect(() => {
    const loadMapData = async () => {
      setLoading(true)
      try {
        const filename = `world_${selectedYear}.geojson`
        const response = await fetch(`../historical-basemaps/geojson/${filename}`)
        if (!response.ok) {
          throw new Error(`Failed to load ${filename}`)
        }
        const data = await response.json()
        setMapData(data)
      } catch (error) {
        console.error('Error loading map data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadMapData()
  }, [selectedYear])

  // Render map using D3
  useEffect(() => {
    if (!mapData || !svgRef.current) return

    const svg = d3.select(svgRef.current)
    svg.selectAll("*").remove() // Clear previous render

    const width = 1200
    const height = 600

    // Set up projection
    const projection = d3.geoNaturalEarth1()
      .scale(180)
      .translate([width / 2, height / 2])

    const path = d3.geoPath().projection(projection)

    // Create main group
    const g = svg.append("g")

    // Color scale for different countries/regions
    const colorScale = d3.scaleOrdinal()
      .domain(mapData.features.map((d, i) => i))
      .range([
        "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
        "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
        "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2",
        "#A3E4D7", "#F9E79F", "#D5A6BD", "#AED6F1", "#A9DFBF",
        "#FAD7A0", "#D2B4DE", "#AED6F1", "#A3E4D7", "#F7DC6F"
      ])

    // Add countries
    g.selectAll(".country")
      .data(mapData.features)
      .enter().append("path")
      .attr("class", "country")
      .attr("d", path)
      .attr("fill", (d, i) => colorScale(i))
      .attr("stroke", "rgba(255, 255, 255, 0.8)")
      .attr("stroke-width", 0.8)
      .style("opacity", 0.85)
      .on("mouseover", function(event, d) {
        d3.select(this).style("opacity", 1)
      })
      .on("mouseout", function() {
        d3.select(this).style("opacity", 0.85)
      })

    // Calculate country sizes and categorize them
    const countriesWithSizes = mapData.features
      .filter(d => d.properties && d.properties.NAME)
      .map(d => {
        const bounds = path.bounds(d)
        const width = bounds[1][0] - bounds[0][0]
        const height = bounds[1][1] - bounds[0][1]
        const area = width * height

        // Categorize countries by size
        let sizeCategory, fontSize, minZoom, textContent
        if (area > 2000) {
          sizeCategory = 'large'
          fontSize = 7
          minZoom = 1.5
          textContent = d.properties.NAME.length > 12 ? d.properties.NAME.substring(0, 12) + "..." : d.properties.NAME
        } else if (area > 500) {
          sizeCategory = 'medium'
          fontSize = 5
          minZoom = 2
          textContent = d.properties.NAME.length > 8 ? d.properties.NAME.substring(0, 8) + "..." : d.properties.NAME
        } else if (area > 100) {
          sizeCategory = 'small'
          fontSize = 4
          minZoom = 3
          // Use abbreviations or short names for small countries
          textContent = getCountryAbbreviation(d.properties.NAME)
        } else {
          sizeCategory = 'tiny'
          fontSize = 3
          minZoom = 4
          // Use country codes for tiny countries
          textContent = getCountryCode(d.properties.NAME)
        }

        return { ...d, area, sizeCategory, fontSize, minZoom, textContent }
      })

    // Add country labels with size-based styling
    g.selectAll(".country-label")
      .data(countriesWithSizes)
      .enter().append("text")
      .attr("class", d => `country-label ${d.sizeCategory}`)
      .attr("transform", function(d) {
        const centroid = path.centroid(d)
        return `translate(${centroid[0]}, ${centroid[1]})`
      })
      .attr("text-anchor", "middle")
      .attr("dy", "0.35em")
      .style("font-size", d => `${d.fontSize}px`)
      .style("font-weight", d => d.sizeCategory === 'large' ? "600" : "500")
      .style("fill", "#2d3748")
      .style("stroke", "white")
      .style("stroke-width", d => d.sizeCategory === 'large' ? "1.5px" : "1px")
      .style("paint-order", "stroke")
      .style("pointer-events", "none")
      .style("opacity", 0) // Initially hidden
      .text(d => d.textContent)

    // Add zoom behavior
    const zoom = d3.zoom()
      .scaleExtent([0.5, 8])
      .on("zoom", (event) => {
        g.attr("transform", event.transform)

        // Progressive label display based on zoom level and country size
        const scale = event.transform.k

        g.selectAll(".country-label")
          .style("opacity", function() {
            const data = d3.select(this).datum()

            // Show labels based on zoom level and country size
            if (scale >= data.minZoom) {
              const fadeInRange = 0.5 // How gradually labels fade in
              const opacity = Math.min(1, (scale - data.minZoom) / fadeInRange + 0.3)
              return opacity
            }
            return 0
          })
          .style("font-size", function() {
            const data = d3.select(this).datum()
            // Scale font size slightly with zoom, but keep size categories distinct
            const scaleFactor = Math.min(1.5, 1 + (scale - 1) * 0.1)
            return `${data.fontSize * scaleFactor}px`
          })
      })

    svg.call(zoom)

  }, [mapData])

  const formatYearDisplay = (year) => {
    if (typeof year === 'string' && year.startsWith('bc')) {
      const bcYear = year.replace('bc', '')
      return `${parseInt(bcYear).toLocaleString()} BC`
    }
    return `${year} AD`
  }

  return (
    <div className="historical-map">
      <div className="controls">
        <div className="year-selector">
          <label htmlFor="year-select">Select Year:</label>
          <select 
            id="year-select"
            value={selectedYear} 
            onChange={(e) => setSelectedYear(e.target.value)}
            disabled={loading}
          >
            {availableYears.map(year => (
              <option key={year} value={year}>
                {formatYearDisplay(year)}
              </option>
            ))}
          </select>
        </div>
        <div className="current-year">
          <h2>{formatYearDisplay(selectedYear)}</h2>
          <p className="zoom-hint">Zoom in to see country names (larger countries appear first)</p>
        </div>
      </div>
      
      <div className="map-container">
        {loading && <div className="loading">Loading map data...</div>}
        <svg
          ref={svgRef}
          width="1200"
          height="600"
          style={{ border: '1px solid #e2e8f0', background: '#f8f9fa', borderRadius: '8px' }}
        />
      </div>
    </div>
  )
}

export default HistoricalMap
