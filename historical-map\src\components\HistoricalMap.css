.historical-map {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.current-year {
  text-align: center;
}

.current-year h2 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 24px;
  font-weight: 700;
}

.zoom-hint {
  margin: 0 0 8px 0;
  color: #718096;
  font-size: 12px;
  font-style: italic;
}

.zoom-info {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 8px;
}

.zoom-level {
  background: rgba(66, 153, 225, 0.1);
  color: #4299e1;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
}

/* Zoom controls styling */
.zoom-controls .zoom-button {
  transition: all 0.2s ease;
}

.zoom-controls .zoom-button:hover {
  fill: rgba(255, 255, 255, 1);
  stroke: #4299e1;
  stroke-width: 2;
}

.map-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
  font-weight: 600;
  color: #4a5568;
  z-index: 10;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.map-container svg {
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  transition: box-shadow 0.3s ease;
}

.map-container svg:hover {
  box-shadow: 0 12px 35px rgba(0,0,0,0.2);
}

/* Country hover effects */
.country {
  cursor: pointer;
  transition: fill 0.2s ease;
}

/* Country labels */
.country-label {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  user-select: none;
  transition: opacity 0.3s ease, font-size 0.2s ease;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
}

/* Size-specific label styles */
.country-label.large {
  font-weight: 600;
  letter-spacing: 0.5px;
}

.country-label.medium {
  font-weight: 500;
  letter-spacing: 0.3px;
}

.country-label.small {
  font-weight: 500;
  letter-spacing: 0.2px;
  text-transform: uppercase;
}

.country-label.tiny {
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-family: 'Courier New', monospace;
}

/* Tooltip styles */
.tooltip {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  z-index: 1000;
}

/* Responsive design */
@media (max-width: 1280px) {
  .map-container svg {
    width: 100%;
    max-width: 1000px;
    height: auto;
  }
}

@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .year-selector {
    flex-direction: column;
    gap: 8px;
  }
  
  .current-year h2 {
    font-size: 20px;
  }
  
  .map-container svg {
    max-width: 100%;
    height: auto;
  }
}
