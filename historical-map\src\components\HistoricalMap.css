.historical-map {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.controls {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.year-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.year-selector label {
  font-weight: 600;
  color: #2d3748;
}

.year-selector select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #2d3748;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.year-selector select:hover {
  border-color: #cbd5e0;
}

.year-selector select:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.year-selector select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.current-year h2 {
  margin: 0;
  color: #2d3748;
  font-size: 24px;
  font-weight: 700;
}

.zoom-hint {
  margin: 5px 0 0 0;
  color: #718096;
  font-size: 12px;
  font-style: italic;
}

.map-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
  font-weight: 600;
  color: #4a5568;
  z-index: 10;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.map-container svg {
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  transition: box-shadow 0.3s ease;
}

.map-container svg:hover {
  box-shadow: 0 12px 35px rgba(0,0,0,0.2);
}

/* Country hover effects */
.country {
  cursor: pointer;
  transition: fill 0.2s ease;
}

/* Tooltip styles */
.tooltip {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  z-index: 1000;
}

/* Responsive design */
@media (max-width: 1280px) {
  .map-container svg {
    width: 100%;
    max-width: 1000px;
    height: auto;
  }
}

@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .year-selector {
    flex-direction: column;
    gap: 8px;
  }
  
  .current-year h2 {
    font-size: 20px;
  }
  
  .map-container svg {
    max-width: 100%;
    height: auto;
  }
}
